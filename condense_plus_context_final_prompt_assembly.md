# condense_plus_context 模式最终提示词拼装详解

## 概述

在 `condense_plus_context` 模式下，最终给 LLM 生成答案的提示词是通过多个组件精心拼装而成的。本文档详细说明了这个拼装过程以及如何自定义最终的提示词结构。

## 最终提示词的组成结构

### 完整的消息流结构

最终发送给 LLM 的是一个 **ChatMessage 列表**，结构如下：

```
[
  ChatMessage(role=SYSTEM, content="context_prompt模板 + system_prompt"),
  ChatMessage(role=USER, content="历史对话消息1"),
  ChatMessage(role=ASSISTANT, content="历史对话回复1"),
  ChatMessage(role=USER, content="历史对话消息2"),
  ChatMessage(role=ASSISTANT, content="历史对话回复2"),
  ...
  ChatMessage(role=USER, content="当前用户问题")
]
```

### 核心拼装逻辑

#### 1. 系统消息的构建

**关键代码位置**: `llama_index/core/chat_engine/utils.py:get_prefix_messages_with_context`

```python
def get_prefix_messages_with_context(
    context_template: PromptTemplate,
    system_prompt: str,
    prefix_messages: List[ChatMessage],
    chat_history: List[ChatMessage],
    llm_metadata_system_role: MessageRole,
) -> List[ChatMessage]:
    # 关键：将 context_prompt 模板和 system_prompt 合并
    context_str_w_sys_prompt = context_template.template + system_prompt.strip()
    return [
        ChatMessage(content=context_str_w_sys_prompt, role=llm_metadata_system_role),
        *prefix_messages,
        *chat_history,
        ChatMessage(content="{query_str}", role=MessageRole.USER),
    ]
```

#### 2. 最终提示词的实际内容

**系统消息内容** (第一条消息):
```
{context_prompt模板内容} + {system_prompt内容}

例如：
"""
The following is a friendly conversation between a user and an AI assistant.
The assistant is talkative and provides lots of specific details from its context.
If the assistant does not know the answer to a question, it truthfully says it
does not know.

Here are the relevant documents for the context:

{context_str}

Instruction: Based on the above documents, provide a detailed answer for the user question below.
Answer "don't know" if not present in the document.
你是一个专业的中文AI助手。
"""
```

**用户消息内容** (最后一条消息):
```
当前用户的问题（原始问题，不是压缩后的问题）
```

## 变量替换过程

### 1. context_str 的填充

在 `Refine.synthesize` 过程中，`{context_str}` 会被替换为检索到的文档内容：

**关键代码位置**: `llama_index/core/response_synthesizers/refine.py:_give_response_single`

```python
# 使用检索到的文档内容填充 context_str
structured_response = program(
    context_str=cur_text_chunk,  # 这里填充检索到的文档
    **response_kwargs,
)
```

### 2. query_str 的填充

`{query_str}` 占位符会被替换为当前用户的问题：

```python
# 在 synthesize 方法中
response = synthesizer.synthesize(message, context_nodes)
# message 就是用户的原始问题
```

## 完整的拼装示例

### 假设场景
- 用户问题: "什么是机器学习？"
- 检索到的文档: "机器学习是人工智能的一个分支..."
- 聊天历史: 用户之前问过"你好"，助手回复了"你好！有什么可以帮助你的吗？"
- 自定义 system_prompt: "你是一个专业的AI助手。"

### 最终发送给LLM的消息结构

```python
[
    ChatMessage(
        role=MessageRole.SYSTEM,
        content="""
The following is a friendly conversation between a user and an AI assistant.
The assistant is talkative and provides lots of specific details from its context.
If the assistant does not know the answer to a question, it truthfully says it
does not know.

Here are the relevant documents for the context:

机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进...

Instruction: Based on the above documents, provide a detailed answer for the user question below.
Answer "don't know" if not present in the document.
你是一个专业的AI助手。
        """
    ),
    ChatMessage(role=MessageRole.USER, content="你好"),
    ChatMessage(role=MessageRole.ASSISTANT, content="你好！有什么可以帮助你的吗？"),
    ChatMessage(role=MessageRole.USER, content="什么是机器学习？")
]
```

## 自定义最终拼装提示词的方法

### 方法1: 自定义 context_prompt

这是**最主要的自定义方式**，因为 `context_prompt` 构成了系统消息的主体：

```python
from llama_index.core.prompts import PromptTemplate

custom_context_prompt = PromptTemplate("""
你是一个专业的知识助手。请严格基于以下提供的文档内容回答用户问题。

=== 相关文档 ===
{context_str}
=== 文档结束 ===

回答要求：
1. 仅基于上述文档内容回答
2. 如果文档中没有相关信息，请明确说明
3. 回答要详细且准确
4. 使用中文回答

现在请回答用户的问题：
""")

chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    context_prompt=custom_context_prompt,
    system_prompt="请保持专业和友好的语调。",
    memory=memory,
    verbose=True
)
```

### 方法2: 结合 system_prompt 进行微调

```python
# context_prompt 负责主要的指令和格式
context_prompt = PromptTemplate("""
基于以下文档回答问题：

{context_str}

请提供准确的回答：
""")

# system_prompt 负责设定整体的行为风格
system_prompt = """
你是一个专业的AI助手。请遵循以下原则：
1. 回答要准确且有依据
2. 语言要简洁明了
3. 对不确定的信息要诚实说明
"""

chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    context_prompt=context_prompt,
    system_prompt=system_prompt,
    memory=memory
)
```

### 方法3: 完全自定义的复杂提示词

```python
advanced_context_prompt = PromptTemplate("""
# AI知识助手系统

## 角色定义
你是一个专业的知识助手，专门基于提供的文档内容回答用户问题。

## 文档内容
```
{context_str}
```

## 回答规则
1. **准确性**: 严格基于上述文档内容
2. **完整性**: 尽可能提供详细信息
3. **诚实性**: 文档中没有的信息要明确说明
4. **结构化**: 使用清晰的段落和要点

## 输出格式
请按以下格式回答：

**回答**: [基于文档的详细回答]

**依据**: [引用的文档片段]

**置信度**: [高/中/低，基于文档支持程度]

现在请回答用户问题：
""")
```

## 重要注意事项

### 1. 变量名称固定
- `{context_str}`: 必须用于接收检索到的文档内容
- 不能更改这个变量名，系统会自动填充

### 2. 消息角色层次
- **SYSTEM消息**: 包含 context_prompt + system_prompt
- **历史对话**: 保持原有的 USER/ASSISTANT 角色
- **当前问题**: 作为最后的 USER 消息

### 3. 提示词长度限制
- 需要考虑 LLM 的上下文窗口限制
- 系统会自动进行文档分块和压缩

### 4. 与 condense_prompt 的区别
- `condense_prompt`: 用于第一阶段的问题压缩
- `context_prompt`: 用于第二阶段的答案生成
- 两者服务于不同的处理阶段

## 调试和验证

### 启用详细日志
```python
chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    context_prompt=custom_context_prompt,
    verbose=True  # 启用详细输出
)
```

### 查看实际发送的消息
```python
# 在聊天前设置日志级别
import logging
logging.getLogger("llama_index.core.chat_engine.condense_plus_context").setLevel(logging.DEBUG)
```

通过理解这个拼装过程，你可以精确控制最终发送给 LLM 的提示词结构，从而获得更好的回答质量。
