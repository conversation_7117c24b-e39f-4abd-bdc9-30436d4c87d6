# LlamaIndex condense_plus_context 聊天模式提示词自定义完整指南

## 概述

`condense_plus_context` 是 LlamaIndex 中一种强大的聊天模式，它结合了对话历史压缩和上下文检索功能。该模式使用了**四种不同的提示词**来完成整个对话流程。

## 工作流程

1. **对话压缩阶段**: 使用 `condense_prompt` 将聊天历史和当前问题压缩为独立问题
2. **上下文检索阶段**: 基于压缩后的问题检索相关文档
3. **响应生成阶段**: 使用 `context_prompt` 基于检索到的上下文生成初始回答
4. **响应优化阶段**: 使用 `context_refine_prompt` 对回答进行优化（如果需要）

## 四种提示词详解

### 1. condense_prompt (对话压缩提示词)

**作用**: 将聊天历史和当前用户问题合并为一个独立的问题

**默认模板**:
```python
DEFAULT_CONDENSE_PROMPT_TEMPLATE = """
Given the following conversation between a user and an AI assistant and a follow up question from user,
rephrase the follow up question to be a standalone question.

Chat History:
{chat_history}
Follow Up Input: {question}
Standalone question:"""
```

**模板变量**:
- `{chat_history}`: 格式化的聊天历史
- `{question}`: 当前用户输入的问题

### 2. context_prompt (上下文提示词)

**作用**: 基于检索到的文档上下文生成回答

**默认模板**:
```python
DEFAULT_CONTEXT_PROMPT_TEMPLATE = """
The following is a friendly conversation between a user and an AI assistant.
The assistant is talkative and provides lots of specific details from its context.
If the assistant does not know the answer to a question, it truthfully says it
does not know.

Here are the relevant documents for the context:

{context_str}

Instruction: Based on the above documents, provide a detailed answer for the user question below.
Answer "don't know" if not present in the document.
"""
```

**模板变量**:
- `{context_str}`: 检索到的相关文档内容

### 3. context_refine_prompt (上下文优化提示词)

**作用**: 使用额外的上下文信息优化已有的回答

**默认模板**:
```python
DEFAULT_CONTEXT_REFINE_PROMPT_TEMPLATE = """
The following is a friendly conversation between a user and an AI assistant.
The assistant is talkative and provides lots of specific details from its context.
If the assistant does not know the answer to a question, it truthfully says it
does not know.

Here are the relevant documents for the context:

{context_msg}

Existing Answer:
{existing_answer}

Instruction: Refine the existing answer using the provided context to assist the user.
If the context isn't helpful, just repeat the existing answer and nothing more.
"""
```

**模板变量**:
- `{context_msg}`: 新的上下文信息
- `{existing_answer}`: 已有的回答

### 4. system_prompt (系统提示词)

**作用**: 设置AI助手的整体行为和角色

**使用方式**: 通过 `system_prompt` 参数传递字符串

## 自定义提示词的方法

### 方法一: 使用 as_chat_engine 方法

```python
from llama_index.core.prompts import PromptTemplate

# 创建自定义提示词
custom_condense_prompt = PromptTemplate("""
请将以下对话历史和用户的后续问题重新表述为一个独立的问题。

对话历史:
{chat_history}
用户问题: {question}
独立问题:
""")

custom_context_prompt = PromptTemplate("""
你是一个专业的AI助手。请基于以下文档内容回答用户问题。

相关文档:
{context_str}

请基于上述文档提供详细准确的回答。如果文档中没有相关信息，请明确说明。
""")

custom_context_refine_prompt = PromptTemplate("""
你是一个专业的AI助手。请使用新的上下文信息优化已有回答。

新的上下文信息:
{context_msg}

现有回答:
{existing_answer}

请使用新信息优化回答，如果新信息无助于改进回答，请保持原回答不变。
""")

# 使用自定义提示词创建聊天引擎
chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    condense_prompt=custom_condense_prompt,
    context_prompt=custom_context_prompt,
    context_refine_prompt=custom_context_refine_prompt,
    system_prompt="你是一个友好且专业的AI助手。",
    memory=memory,
    verbose=True
)
```

### 方法二: 直接使用 CondensePlusContextChatEngine.from_defaults

```python
from llama_index.core.chat_engine.condense_plus_context import CondensePlusContextChatEngine

chat_engine = CondensePlusContextChatEngine.from_defaults(
    retriever=index.as_retriever(),
    condense_prompt=custom_condense_prompt,
    context_prompt=custom_context_prompt,
    context_refine_prompt=custom_context_refine_prompt,
    system_prompt="你是一个友好且专业的AI助手。",
    memory=memory,
    verbose=True
)
```

## 提示词格式要求

### 字符串格式
可以直接传递字符串，系统会自动转换为 PromptTemplate：

```python
condense_prompt = """
自定义的压缩提示词模板
{chat_history}
{question}
"""
```

### PromptTemplate 对象
推荐使用 PromptTemplate 对象以获得更好的控制：

```python
from llama_index.core.prompts import PromptTemplate

condense_prompt = PromptTemplate("""
自定义的压缩提示词模板
{chat_history}
{question}
""")
```

## 重要注意事项

1. **必需的模板变量**:
   - `condense_prompt`: 必须包含 `{chat_history}` 和 `{question}`
   - `context_prompt`: 必须包含 `{context_str}`
   - `context_refine_prompt`: 必须包含 `{context_msg}` 和 `{existing_answer}`

2. **参数传递**: 所有自定义提示词参数都会通过 `**kwargs` 传递给底层的 `CondensePlusContextChatEngine.from_defaults` 方法

3. **系统提示词**: `system_prompt` 是独立的参数，用于设置整体的AI行为，不是模板格式

4. **跳过压缩**: 可以通过 `skip_condense=True` 跳过对话压缩步骤

## 完整示例

```python
from llama_index.core.prompts import PromptTemplate
from llama_index.core.memory import ChatMemoryBuffer

# 定义所有自定义提示词
custom_prompts = {
    "condense_prompt": PromptTemplate("""
    请将对话历史和新问题合并为独立问题:
    
    历史对话:
    {chat_history}
    
    新问题: {question}
    
    独立问题:
    """),
    
    "context_prompt": PromptTemplate("""
    基于以下文档回答问题:
    
    {context_str}
    
    请提供准确详细的回答:
    """),
    
    "context_refine_prompt": PromptTemplate("""
    使用新信息优化回答:
    
    新信息: {context_msg}
    现有回答: {existing_answer}
    
    优化后的回答:
    """),
    
    "system_prompt": "你是一个专业的中文AI助手。"
}

# 创建聊天引擎
memory = ChatMemoryBuffer.from_defaults()
chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    memory=memory,
    verbose=True,
    **custom_prompts
)

# 开始对话
response = chat_engine.chat("你好，请介绍一下这个项目")
```

## 总结

`condense_plus_context` 模式提供了四个层次的提示词自定义能力，允许开发者精确控制对话的每个阶段。通过合理配置这些提示词，可以显著提升聊天机器人的表现和用户体验。
