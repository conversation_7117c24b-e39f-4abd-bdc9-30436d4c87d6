# 如何查看后端发送给LLM的最终信息

## 概述

在使用 LlamaIndex 的 `condense_plus_context` 模式时，有多种方法可以查看后端实际发送给 LLM 的完整信息。本文档提供了从简单到高级的各种监控方法。

## 方法一：启用 verbose 模式（最简单）

### 基本用法

```python
from llama_index.core.memory import ChatMemoryBuffer

memory = ChatMemoryBuffer.from_defaults()

chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    memory=memory,
    verbose=True  # 启用详细输出
)

# 进行对话时会在控制台输出详细信息
response = chat_engine.chat("你的问题")
```

### 输出内容
- 压缩后的问题
- 检索到的文档片段
- 部分处理过程信息

## 方法二：使用 SimpleLLMHandler（推荐）

### 完整示例

```python
import logging
from llama_index.core.callbacks import CallbackManager
from llama_index.core.callbacks.simple_llm_handler import SimpleLLMHandler

# 设置日志记录器
logger = logging.getLogger("llm_monitor")
logger.setLevel(logging.INFO)

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# 设置日志格式
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 创建 LLM 监控处理器
llm_handler = SimpleLLMHandler(logger=logger)

# 创建回调管理器
callback_manager = CallbackManager([llm_handler])

# 创建聊天引擎
chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    callback_manager=callback_manager,
    memory=memory,
    verbose=True
)

# 进行对话
response = chat_engine.chat("什么是机器学习？")
```

### 输出格式
```
** Messages: **
system: The following is a friendly conversation between a user and an AI assistant...

Here are the relevant documents for the context:

[检索到的文档内容]

Instruction: Based on the above documents, provide a detailed answer...
user: 你好
assistant: 你好！有什么可以帮助你的吗？
user: 什么是机器学习？

**************************************************
** Response: **
[LLM的回复内容]
**************************************************
```

## 方法三：使用 LlamaDebugHandler（高级调试）

### 完整示例

```python
from llama_index.core.callbacks import CallbackManager, LlamaDebugHandler, CBEventType, EventPayload

# 创建调试处理器
debug_handler = LlamaDebugHandler(print_trace_on_end=True)

# 创建回调管理器
callback_manager = CallbackManager([debug_handler])

# 创建聊天引擎
chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    callback_manager=callback_manager,
    memory=memory
)

# 进行对话
response = chat_engine.chat("什么是机器学习？")

# 查看所有LLM事件
llm_events = debug_handler.get_events(CBEventType.LLM)
for event in llm_events:
    if event.payload:
        if EventPayload.MESSAGES in event.payload:
            messages = event.payload[EventPayload.MESSAGES]
            print("=== 发送给LLM的消息 ===")
            for msg in messages:
                print(f"{msg.role}: {msg.content}")
            print("=" * 50)
        
        if EventPayload.RESPONSE in event.payload:
            response = event.payload[EventPayload.RESPONSE]
            print(f"=== LLM回复 ===\n{response}")
            print("=" * 50)
```

## 方法四：自定义回调处理器（最灵活）

### 创建自定义处理器

```python
from llama_index.core.callbacks.base_handler import BaseCallbackHandler
from llama_index.core.callbacks.schema import CBEventType, EventPayload
from typing import Any, Dict, Optional, List
import json

class CustomLLMMonitor(BaseCallbackHandler):
    """自定义LLM监控处理器"""
    
    def __init__(self):
        super().__init__()
        self.llm_calls = []
    
    def on_event_start(
        self,
        event_type: CBEventType,
        payload: Optional[Dict[str, Any]] = None,
        event_id: str = "",
        parent_id: str = "",
        **kwargs: Any,
    ) -> str:
        if event_type == CBEventType.LLM and payload:
            call_info = {
                "event_id": event_id,
                "type": "start",
                "timestamp": self._get_timestamp(),
            }
            
            # 捕获发送给LLM的消息
            if EventPayload.MESSAGES in payload:
                messages = payload[EventPayload.MESSAGES]
                call_info["messages"] = [
                    {"role": msg.role.value, "content": str(msg.content)}
                    for msg in messages
                ]
                
                # 打印完整的消息结构
                print("\n" + "="*80)
                print("🚀 发送给LLM的完整消息:")
                print("="*80)
                for i, msg in enumerate(messages):
                    print(f"消息 {i+1} [{msg.role.value.upper()}]:")
                    print("-" * 40)
                    print(msg.content)
                    print("-" * 40)
                print("="*80 + "\n")
            
            # 捕获提示词（用于completion模式）
            if EventPayload.PROMPT in payload:
                prompt = payload[EventPayload.PROMPT]
                call_info["prompt"] = str(prompt)
                
                print("\n" + "="*80)
                print("🚀 发送给LLM的提示词:")
                print("="*80)
                print(prompt)
                print("="*80 + "\n")
            
            self.llm_calls.append(call_info)
        
        return event_id
    
    def on_event_end(
        self,
        event_type: CBEventType,
        payload: Optional[Dict[str, Any]] = None,
        event_id: str = "",
        **kwargs: Any,
    ) -> None:
        if event_type == CBEventType.LLM and payload:
            # 捕获LLM的回复
            if EventPayload.RESPONSE in payload:
                response = payload[EventPayload.RESPONSE]
                print("\n" + "="*80)
                print("📥 LLM回复:")
                print("="*80)
                print(str(response))
                print("="*80 + "\n")
            
            # 更新调用信息
            for call in reversed(self.llm_calls):
                if call["event_id"] == event_id and call["type"] == "start":
                    call["response"] = str(payload.get(EventPayload.RESPONSE, ""))
                    call["end_timestamp"] = self._get_timestamp()
                    break
    
    def _get_timestamp(self):
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_all_calls(self):
        """获取所有LLM调用记录"""
        return self.llm_calls
    
    def save_calls_to_file(self, filename: str):
        """保存调用记录到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.llm_calls, f, ensure_ascii=False, indent=2)

# 使用自定义监控器
monitor = CustomLLMMonitor()
callback_manager = CallbackManager([monitor])

chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    callback_manager=callback_manager,
    memory=memory
)

# 进行对话
response = chat_engine.chat("什么是机器学习？")

# 查看所有调用记录
all_calls = monitor.get_all_calls()
print(f"总共进行了 {len(all_calls)} 次LLM调用")

# 保存到文件
monitor.save_calls_to_file("llm_calls.json")
```

## 方法五：使用 Python 日志系统

### 配置日志

```python
import logging

# 配置根日志记录器
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('llamaindex_debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 设置特定模块的日志级别
logging.getLogger("llama_index.core.chat_engine.condense_plus_context").setLevel(logging.DEBUG)
logging.getLogger("llama_index.core.llms").setLevel(logging.DEBUG)
logging.getLogger("llama_index.core.response_synthesizers").setLevel(logging.DEBUG)

# 创建聊天引擎
chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    memory=memory,
    verbose=True
)

# 进行对话
response = chat_engine.chat("什么是机器学习？")
```

## 方法六：组合使用多种方法

### 完整的监控设置

```python
import logging
from llama_index.core.callbacks import CallbackManager
from llama_index.core.callbacks.simple_llm_handler import SimpleLLMHandler

# 1. 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("comprehensive_monitor")

# 2. 创建多个处理器
simple_handler = SimpleLLMHandler(logger=logger)
custom_monitor = CustomLLMMonitor()

# 3. 创建回调管理器
callback_manager = CallbackManager([simple_handler, custom_monitor])

# 4. 创建聊天引擎
chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    callback_manager=callback_manager,
    memory=memory,
    verbose=True  # 同时启用verbose模式
)

# 5. 进行对话
response = chat_engine.chat("什么是机器学习？")

# 6. 分析结果
print(f"\n📊 监控总结:")
print(f"LLM调用次数: {len(custom_monitor.get_all_calls())}")
custom_monitor.save_calls_to_file("detailed_llm_calls.json")
```

## 重要提示

### 1. 性能考虑
- 启用详细监控会影响性能
- 生产环境中建议只启用必要的监控

### 2. 隐私安全
- 监控日志可能包含敏感信息
- 注意日志文件的存储和访问权限

### 3. 调试技巧
- 使用 `verbose=True` 进行快速调试
- 使用自定义处理器进行深度分析
- 结合多种方法获得完整视图

### 4. 常见问题
- 如果看不到消息内容，检查日志级别设置
- 确保回调管理器正确传递给聊天引擎
- 某些LLM提供商可能有额外的日志记录机制

通过这些方法，你可以完全透明地查看 LlamaIndex 发送给 LLM 的所有信息，包括完整的消息结构、上下文内容和用户问题。
