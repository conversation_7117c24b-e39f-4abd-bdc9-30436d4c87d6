[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "black>=23.7.0,<=24.3.0",
    "boto3==1.33.6",
    "botocore>=1.33.13",
    "codespell[toml]>=v2.2.6",
    "diff-cover>=9.2.0",
    "llama-cloud>=0.0.6",
    "mypy==1.11.0",
    "openai",
    "pandas",
    "pre-commit==3.2.0",
    "pylint==2.15.10",
    "pytest~=7.2",
    "pytest-asyncio==0.21.0",
    "pytest-cov~=5.0",
    "pytest-dotenv==0.5.2",
    "pytest-mock==3.11.1",
    "rake-nltk==1.0.6",
    "ruff==0.11.11",
    "tree-sitter",
    "tree-sitter-language-pack",
    "types-Deprecated>=0.1.0",
    "types-PyYAML>=*********,<7",
    "types-requests>=*********",
]

[project]
name = "llama-index-core"
version = "0.13.2"
description = "Interface between LLMs and your data"
authors = [{name = "<PERSON>", email = "<EMAIL>"}]
requires-python = ">=3.9,<4.0"
readme = "README.md"
license = "MIT"
maintainers = [
    {name = "Andrei Fajardo", email = "<EMAIL>"},
    {name = "Haotian Zhang", email = "<EMAIL>"},
    {name = "Jerry Liu", email = "<EMAIL>"},
    {name = "Logan Markewich", email = "<EMAIL>"},
    {name = "Simon Suo", email = "<EMAIL>"},
    {name = "Sourabh Desai", email = "<EMAIL>"},
]
keywords = ["LLM", "NLP", "RAG", "data", "devtools", "index", "retrieval"]
classifiers = [
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "SQLAlchemy[asyncio]>=1.4.49",
    "dataclasses-json",
    "deprecated>=*******",
    "fsspec>=2023.5.0",
    "httpx",
    "nest-asyncio>=1.5.8,<2",
    "nltk>3.8.1",
    "numpy",
    "tenacity>=8.2.0,!=8.4.0,<10.0.0",
    "tiktoken>=0.7.0",
    "typing-extensions>=4.5.0",
    "typing-inspect>=0.8.0",
    "requests>=2.31.0",
    "aiohttp>=3.8.6,<4",
    "networkx>=3.0",
    "dirtyjson>=1.0.8,<2",
    "tqdm>=4.66.1,<5",
    "pillow>=9.0.0",
    "PyYAML>=6.0.1",
    "wrapt",
    "pydantic>=2.8.0",
    "filetype>=1.2.0,<2",
    "eval-type-backport>=0.2.0,<0.3 ; python_version < '3.10'",
    "banks>=2.2.0,<3",
    "aiosqlite",
    "llama-index-workflows>=1.0.1,<2",
    "setuptools>=80.9.0",
    "platformdirs",
]

[project.urls]
Homepage = "https://llamaindex.ai"
Repository = "https://github.com/run-llama/llama_index"
Documentation = "https://docs.llamaindex.ai/en/stable/"

[tool.codespell]
check-filenames = true
check-hidden = true
ignore-words-list = "astroid,gallary,momento,narl,ot,rouge"
# Feel free to un-skip examples, and experimental, you will just need to
# work through many typos (--write-changes and --interactive will help)
skip = "./llama_index/core/_static,./examples,./experimental,*.csv,*.html,*.json,*.jsonl,*.pdf,*.txt,*.ipynb"

[tool.coverage.run]
omit = [
    "llama_index/core/instrumentation/*",
    "llama_index/core/workflow/*",
    "tests/*",
]

[tool.hatch.build.targets.sdist]
include = [
    "llama_index",
    "llama_index/core/_static/nltk_cache/corpora/stopwords/**",
    "llama_index/core/_static/nltk_cache/tokenizers/punkt_tab/**",
    "llama_index/core/_static/tiktoken_cache/**",
]
exclude = ["**/BUILD"]

[tool.hatch.build.targets.wheel]
include = [
    "llama_index",
    "llama_index/core/_static/nltk_cache/corpora/stopwords/**",
    "llama_index/core/_static/nltk_cache/tokenizers/punkt_tab/**",
    "llama_index/core/_static/tiktoken_cache/**",
]
exclude = ["**/BUILD"]

[tool.mypy]
disallow_untyped_defs = true
# Remove venv skip when integrated with pre-commit
exclude = ["_static", "build", "examples", "notebooks", "venv"]
explicit_package_bases = true
ignore_missing_imports = true
namespace_packages = true
plugins = "pydantic.mypy"
python_version = "3.10"

[tool.tomlsort]
all = true
in_place = true
spaces_before_inline_comment = 2  # Match Python PEP 8
spaces_indent_inline_array = 4  # Match Python PEP 8
trailing_comma_inline_array = true

[tool.tomlsort.overrides."tool.poetry.dependencies"]
table_keys = false

[tool.uv]

[[tool.uv.index]]
name = "nvidia-pypi"
url = "https://pypi.nvidia.com"
