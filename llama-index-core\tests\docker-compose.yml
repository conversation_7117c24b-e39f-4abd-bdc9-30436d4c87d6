version: "3"

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.10.0 # https://www.docker.elastic.co/r/elasticsearch/elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false # security has been disabled, so no login or password is required.
      - xpack.security.http.ssl.enabled=false
      - xpack.license.self_generated.type=trial
    ports:
      - "9200:9200"
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "curl --silent --fail http://localhost:9200/_cluster/health || exit 1",
        ]
      interval: 10s
      retries: 60

  postgres:
    build:
      context: ./initialization/postgres
      dockerfile: Dockerfile
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: mark90
      PGPASSWORD: mark90
    expose:
      - "5432"
    ports:
      - 5432:5432
    volumes:
      - ./initialization/postgres/:/docker-entrypoint-initdb.d
  chroma:
    image: ghcr.io/chroma-core/chroma:latest
    ports:
      - 8000:8000
