# text_qa_template 详细介绍

## 概述

`text_qa_template` 是 LlamaIndex 响应合成器（Response Synthesizer）中的核心组件，负责定义如何将检索到的文档内容和用户问题组合成发送给 LLM 的最终提示词。

## 基本概念

### 1. **定义**
`text_qa_template` 是一个提示词模板，用于指导 LLM 如何基于提供的上下文信息回答用户问题。

### 2. **核心变量**
- `{context_str}`: 检索到的文档内容
- `{query_str}`: 用户的问题

### 3. **默认模板**

<augment_code_snippet path="llama-index-core\llama_index\core\prompts\default_prompts.py" mode="EXCERPT">
```python
DEFAULT_TEXT_QA_PROMPT_TMPL = (
    "Context information is below.\n"
    "---------------------\n"
    "{context_str}\n"
    "---------------------\n"
    "Given the context information and not prior knowledge, "
    "answer the query.\n"
    "Query: {query_str}\n"
    "Answer: "
)
```
</augment_code_snippet>

## 在 condense_plus_context 中的作用

### 1. **与 context_prompt 的关系**

在 `condense_plus_context` 模式中，`text_qa_template` 实际上就是由你的 `context_prompt` 构建的：

<augment_code_snippet path="llama-index-core\llama_index\core\chat_engine\utils.py" mode="EXCERPT">
```python
def get_response_synthesizer(
    llm: LLM,
    callback_manager: CallbackManager,
    qa_messages: List[ChatMessage],
    refine_messages: List[ChatMessage],
    streaming: bool = False,
    qa_function_mappings: Optional[Dict[str, Callable]] = None,
    refine_function_mappings: Optional[Dict[str, Callable]] = None,
) -> CompactAndRefine:
    return CompactAndRefine(
        llm=llm,
        callback_manager=callback_manager,
        text_qa_template=ChatPromptTemplate.from_messages(
            qa_messages,  # 这里包含了你的 context_prompt
            function_mappings=qa_function_mappings,
        ),
        # ...
    )
```
</augment_code_snippet>

### 2. **处理流程**

1. **模板构建**: `context_prompt` + `system_prompt` + `chat_history` → `text_qa_template`
2. **变量填充**: `{query_str}` 被用户问题替换
3. **上下文注入**: `{context_str}` 被检索到的文档替换
4. **发送给LLM**: 最终的完整提示词

## 使用机制详解

### 1. **partial_format 过程**

<augment_code_snippet path="llama-index-core\llama_index\core\response_synthesizers\refine.py" mode="EXCERPT">
```python
def _give_response_single(
    self,
    query_str: str,
    text_chunk: str,
    **response_kwargs: Any,
) -> RESPONSE_TEXT_TYPE:
    """Give response given a query and a corresponding text chunk."""
    text_qa_template = self._text_qa_template.partial_format(query_str=query_str)
    # ...
```
</augment_code_snippet>

这里发生的事情：
- `{query_str}` 被用户的实际问题替换
- `{context_str}` 保留，等待后续填充

### 2. **context_str 填充**

```python
structured_response = program(
    context_str=cur_text_chunk,  # 这里填充检索到的文档
    **response_kwargs,
)
```

## 不同类型的 text_qa_template

### 1. **普通 PromptTemplate**（用于非聊天模型）

```python
DEFAULT_TEXT_QA_PROMPT_TMPL = (
    "Context information is below.\n"
    "---------------------\n"
    "{context_str}\n"
    "---------------------\n"
    "Given the context information and not prior knowledge, "
    "answer the query.\n"
    "Query: {query_str}\n"
    "Answer: "
)
```

### 2. **ChatPromptTemplate**（用于聊天模型）

<augment_code_snippet path="llama-index-core\llama_index\core\prompts\chat_prompts.py" mode="EXCERPT">
```python
TEXT_QA_PROMPT_TMPL_MSGS = [
    TEXT_QA_SYSTEM_PROMPT,  # 系统消息
    ChatMessage(
        content=(
            "Context information is below.\n"
            "---------------------\n"
            "{context_str}\n"
            "---------------------\n"
            "Given the context information and not prior knowledge, "
            "answer the query.\n"
            "Query: {query_str}\n"
            "Answer: "
        ),
        role=MessageRole.USER,
    ),
]
```
</augment_code_snippet>

## 在 condense_plus_context 中的实际应用

### 1. **模板转换过程**

你的 `context_prompt`:
```python
custom_context_prompt = PromptTemplate("""
基于以下文档回答问题：

{context_str}

请提供详细回答：
""")
```

经过 `get_prefix_messages_with_context` 处理后变成：
```python
[
    ChatMessage(role=SYSTEM, content="基于以下文档回答问题：\n\n{context_str}\n\n请提供详细回答："),
    *chat_history,
    ChatMessage(role=USER, content="{query_str}"),
]
```

### 2. **最终的 text_qa_template**

这个 ChatMessage 列表被转换为 `ChatPromptTemplate`，成为 `text_qa_template`。

### 3. **变量替换顺序**

1. **第一步**: `partial_format(query_str=用户问题)`
   ```python
   [
       ChatMessage(role=SYSTEM, content="基于以下文档回答问题：\n\n{context_str}\n\n请提供详细回答："),
       *chat_history,
       ChatMessage(role=USER, content="什么是机器学习？"),  # query_str 被替换
   ]
   ```

2. **第二步**: `program(context_str=检索到的文档)`
   ```python
   [
       ChatMessage(role=SYSTEM, content="基于以下文档回答问题：\n\n机器学习是...\n\n请提供详细回答："),  # context_str 被替换
       *chat_history,
       ChatMessage(role=USER, content="什么是机器学习？"),
   ]
   ```

## 自定义 text_qa_template 的方法

### 1. **通过 context_prompt 自定义**（推荐）

```python
custom_context_prompt = PromptTemplate("""
你是一个专业的AI助手。请基于以下文档内容回答用户问题。

=== 文档内容 ===
{context_str}
=== 文档结束 ===

回答要求：
1. 仅基于文档内容回答
2. 回答要详细准确
3. 如果文档中没有相关信息，请明确说明
""")

chat_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    context_prompt=custom_context_prompt,
    memory=memory
)
```

### 2. **直接自定义 text_qa_template**（高级用法）

```python
from llama_index.core.response_synthesizers import get_response_synthesizer
from llama_index.core.prompts import ChatPromptTemplate

custom_text_qa_template = ChatPromptTemplate.from_messages([
    ChatMessage(role=MessageRole.SYSTEM, content="你是一个专业的AI助手。"),
    ChatMessage(role=MessageRole.USER, content="""
    基于以下文档回答问题：
    
    {context_str}
    
    问题：{query_str}
    
    请提供详细回答：
    """)
])

synthesizer = get_response_synthesizer(
    text_qa_template=custom_text_qa_template,
    # 其他参数...
)
```

## 重要注意事项

### 1. **变量名称固定**
- `{context_str}`: 必须用于接收文档内容
- `{query_str}`: 必须用于接收用户问题
- 这些变量名不能更改

### 2. **模板类型选择**
- **聊天模型**: 使用 `ChatPromptTemplate`
- **完成模型**: 使用 `PromptTemplate`

### 3. **与其他提示词的区别**
- **context_prompt**: 在 condense_plus_context 中构建系统消息
- **text_qa_template**: 在响应合成器中处理文档和问题
- **condense_prompt**: 用于问题压缩，完全不同的阶段

### 4. **调试技巧**
```python
# 查看实际的 text_qa_template
synthesizer = chat_engine._get_response_synthesizer([])
print(synthesizer._text_qa_template.get_template())
```

## 总结

`text_qa_template` 是连接检索到的文档和用户问题的桥梁，在 `condense_plus_context` 模式中，它由你的 `context_prompt` 构建而成。理解这个机制有助于更好地自定义聊天引擎的行为，确保 LLM 能够正确理解任务并基于提供的上下文生成高质量的回答。
